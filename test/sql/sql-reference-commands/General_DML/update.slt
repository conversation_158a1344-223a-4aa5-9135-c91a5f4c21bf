exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE target (k INT, v INT);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE src (k INT, v INT);

exclude-from-coverage
statement ok
INSERT INTO target (K, V)
VALUES (0, 10);

exclude-from-coverage
statement ok
INSERT INTO src (K, V)
VALUES
  (0, 11),
  (0, 12),
  (0, 13);

statement ok
UPDATE target  
  SET v = src.v  
  FROM src  
  WHERE target.k = src.k;

statement ok
UPDATE target SET v = b.v  
  FROM (SELECT k, MIN(v) v FROM src GROUP BY k) b  
  WHERE target.k = b.k

query TT
SELECT * FROM target;
----
0	11

query TT
SELECT * FROM src;
----
0	11
0	12
0	13

# Test UPDATE...FROM when join produces multiple matches for a row

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE upd_tgt(id INT, val STRING);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE upd_src(id INT, newval STRING);

exclude-from-coverage
statement ok
INSERT INTO upd_tgt VALUES (1,'Old'),(2,'Two');

exclude-from-coverage
statement ok
INSERT INTO upd_src VALUES (1,'New'),(1,'Newer');  -- duplicate 'id=1' in source

statement ok
UPDATE upd_tgt AS t
SET val = s.newval
FROM upd_src AS s
WHERE t.id = s.id;

# Test UPDATE setting a column using a subquery on the same table

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE upd_t2(id INT, val INT);

exclude-from-coverage
statement ok
INSERT INTO upd_t2 VALUES (1,10),(2,20),(3,30);

statement ok
UPDATE upd_t2
SET val = (SELECT SUM(val) FROM upd_t2) / 10;

query I
SELECT DISTINCT val FROM upd_t2 ORDER BY val;
----
6

