exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE t1 (
  id NUMBER(8) NOT NULL,
  c1 VARCHAR(255)
);

statement ok
ALTER TABLE t1 SET CHANGE_TRACKING = TRUE;

exclude-from-coverage
statement ok
SET ts1 = CURRENT_TIMESTAMP();

exclude-from-coverage
statement ok
INSERT INTO t1 (id, c1) VALUES
  (1, 'red'),
  (2, 'blue'),
  (3, 'green');

exclude-from-coverage
statement ok
DELETE FROM t1 WHERE id = 1;

exclude-from-coverage
statement ok
UPDATE t1 SET c1 = 'purple' WHERE id = 2;

query TTTT
SELECT id, c1, METADATA$ACTION, METADATA$ISUPDATE
FROM t1
  CHANGES(INFORMATION => DEFAULT)
  AT(TIMESTAMP => $ts1);
----
2	purple	INSERT	FALSE
3	green	INSERT	FALSE

query TTTT
SELECT id, c1, METADATA$ACTION, METADATA$ISUPDATE
FROM t1
  CHANGES(INFORMATION => APPEND_ONLY)
  AT(TIMESTAMP => $ts1);
----
1	red	INSERT	FALSE
2	blue	INSERT	FALSE
3	green	INSERT	FALSE