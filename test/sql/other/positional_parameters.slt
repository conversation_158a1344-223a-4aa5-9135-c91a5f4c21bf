# Test basic positional parameter usage
query T
SELECT $1 as value FROM VALUES (1), (2), (3)
----
1
2
3

# Test positional parameters with multiple columns
query TT
SELECT $1 as first_col, $2 as second_col FROM VALUES (1, 'one'), (2, 'two'), (3, 'three')
----
1	one
2	two
3	three

# Test positional parameters in aggregated functions - SUM
query T
SELECT SUM($1) as total FROM VALUES (10), (20), (30)
----
60

# Test positional parameters in aggregated functions - MAX
query T
SELECT MAX($1) as maximum FROM VALUES (10), (20), (30)
----
30

# Test positional parameters in aggregated functions - MIN
query T
SELECT MIN($1) as minimum FROM VALUES (10), (20), (30)
----
10

# Test positional parameters in aggregated functions - AVG
query T
SELECT AVG($1) as average FROM VALUES (10), (20), (30)
----
20.000000

# Test positional parameters in aggregated functions - COUNT
query T
SELECT COUNT($1) as count_values FROM VALUES (10), (20), (30)
----
3

# Test multiple positional parameters in different aggregated functions
query TTT
SELECT SUM($1) as sum_first, MAX($2) as max_second, AVG($1) as avg_first
FROM VALUES (10, 100), (20, 200), (30, 300)
----
60	300	20.000000