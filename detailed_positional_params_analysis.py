#!/usr/bin/env python3
"""
Detailed analysis of positional parameters usage in .slt files.
Shows the context and usage patterns for each file.
"""

import os
import re
from typing import List, Dict, <PERSON><PERSON>


def analyze_positional_params_usage(file_path: str) -> Dict:
    """
    Analyze how positional parameters are used in a specific file.
    
    Returns:
        Dictionary with analysis results
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return {"error": f"Error reading file: {e}"}
    
    lines = content.split('\n')
    
    # Find all positional parameter usages
    param_pattern = r'\$(\d+)'
    brace_pattern = r'\$\{(\d+)\}'
    
    usages = []
    context_lines = 2  # Show 2 lines before and after
    
    for line_num, line in enumerate(lines, 1):
        # Check for both patterns
        params_found = []
        
        # Standard $1, $2, etc.
        for match in re.finditer(param_pattern, line):
            params_found.append(f"${match.group(1)}")
        
        # Braced ${1}, ${2}, etc.
        for match in re.finditer(brace_pattern, line):
            params_found.append(f"${{{match.group(1)}}}")
        
        if params_found:
            # Get context lines
            start_line = max(0, line_num - context_lines - 1)
            end_line = min(len(lines), line_num + context_lines)
            
            context = []
            for i in range(start_line, end_line):
                prefix = ">>> " if i == line_num - 1 else "    "
                context.append(f"{prefix}{i+1:3}: {lines[i]}")
            
            usages.append({
                "line_number": line_num,
                "line_content": line.strip(),
                "parameters": params_found,
                "context": context
            })
    
    # Analyze usage patterns
    all_params = set()
    for usage in usages:
        all_params.update(usage["parameters"])
    
    # Determine the likely purpose based on file path and content
    purpose = "Unknown"
    if "result_scan" in file_path.lower():
        purpose = "RESULT_SCAN function testing - referencing columns from previous query results"
    elif "pivot" in file_path.lower():
        purpose = "PIVOT operation - referencing columns in pivot aggregation"
    elif "values" in file_path.lower():
        purpose = "VALUES clause - referencing unnamed columns in temporary result sets"
    elif "to_char" in file_path.lower() or "to_decimal" in file_path.lower():
        purpose = "Format strings - $ symbols in number formatting patterns (not true positional params)"
    elif "arrays_zip" in file_path.lower():
        purpose = "JSON/Array operations - $ symbols in JSON key names (not true positional params)"
    
    return {
        "file_path": file_path,
        "total_usages": len(usages),
        "unique_parameters": sorted(all_params),
        "purpose": purpose,
        "usages": usages
    }


def main():
    """
    Main function to provide detailed analysis of positional parameter usage.
    """
    # Files found by the previous script
    files_with_params = [
        "test/sql/sql-reference-commands/Query_syntax/pivot.slt",
        "test/sql/sql-reference-commands/Query_syntax/values.slt", 
        "test/sql/sql-reference-functions/Conversion/to_char.slt",
        "test/sql/sql-reference-functions/Conversion/to_decimal.slt",
        "test/sql/sql-reference-functions/Conversion/try_to_decimal.slt",
        "test/sql/sql-reference-functions/Semi-structured_and_structured_data/arrays_zip.slt",
        "test/sql/sql-reference-functions/Table/result_scan.slt"
    ]
    
    print("DETAILED ANALYSIS OF POSITIONAL PARAMETER USAGE")
    print("=" * 80)
    
    true_positional_params = []
    format_string_usage = []
    
    for file_path in files_with_params:
        if not os.path.exists(file_path):
            print(f"⚠️  File not found: {file_path}")
            continue
            
        analysis = analyze_positional_params_usage(file_path)
        
        if "error" in analysis:
            print(f"❌ {file_path}: {analysis['error']}")
            continue
        
        print(f"\n📁 {analysis['file_path']}")
        print(f"   Purpose: {analysis['purpose']}")
        print(f"   Parameters: {analysis['unique_parameters']}")
        print(f"   Total usages: {analysis['total_usages']}")
        
        # Categorize based on purpose
        if ("result_scan" in analysis['purpose'].lower() or
            "values" in analysis['purpose'].lower() or
            "pivot" in analysis['purpose'].lower()):
            true_positional_params.append(file_path)
        else:
            format_string_usage.append(file_path)
        
        # Show a few examples
        print("   Examples:")
        for i, usage in enumerate(analysis['usages'][:2]):  # Show first 2 examples
            print(f"   Example {i+1} (Line {usage['line_number']}):")
            for context_line in usage['context']:
                print(f"     {context_line}")
            print(f"     Parameters found: {', '.join(usage['parameters'])}")
            print()
        
        if len(analysis['usages']) > 2:
            print(f"   ... and {len(analysis['usages']) - 2} more usages")
        
        print("-" * 60)
    
    # Summary
    print("\n" + "=" * 80)
    print("CLASSIFICATION SUMMARY:")
    print(f"Total files analyzed: {len(files_with_params)}")
    
    print(f"\n🎯 TRUE POSITIONAL PARAMETERS ({len(true_positional_params)} files):")
    print("   These files use $1, $2, etc. to reference columns by position")
    for file_path in true_positional_params:
        print(f"   - {file_path}")
    
    print(f"\n💰 FORMAT STRINGS / JSON KEYS ({len(format_string_usage)} files):")
    print("   These files use $ in format strings or JSON, not as positional parameters")
    for file_path in format_string_usage:
        print(f"   - {file_path}")
    
    print(f"\n📋 RECOMMENDATION:")
    if true_positional_params:
        print(f"   Focus on the {len(true_positional_params)} files with true positional parameters")
        print("   for understanding $1, $2, etc. column reference functionality.")
    else:
        print("   Most $ usages are in format strings, not true positional parameters.")


if __name__ == "__main__":
    main()
