#!/usr/bin/env python3
"""
Script to find all .slt files in test/sql directory that use positional parameter syntax.
Searches for patterns like $1, $2, $3, etc. and ${1}, ${2}, etc.
"""

import os
import re
import sys
from typing import List, Dict, <PERSON><PERSON>


def find_positional_params_in_file(file_path: str) -> List[Tuple[int, str, List[str]]]:
    """
    Find all positional parameters in a file.
    
    Returns:
        List of tuples: (line_number, line_content, [matched_params])
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []
    
    # Regex patterns for positional parameters
    patterns = [
        r'\$(\d+)',           # $1, $2, $3, etc.
        r'\$\{(\d+)\}',       # ${1}, ${2}, ${3}, etc.
    ]
    
    matches = []
    lines = content.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        found_params = []
        
        for pattern in patterns:
            param_matches = re.findall(pattern, line)
            if param_matches:
                # Convert to the format we found (e.g., $1, ${2})
                if pattern.startswith(r'\$\{'):
                    found_params.extend([f"${{{param}}}" for param in param_matches])
                else:
                    found_params.extend([f"${param}" for param in param_matches])
        
        if found_params:
            matches.append((line_num, line.strip(), found_params))
    
    return matches


def get_all_slt_files(slt_dir: str = "test/sql") -> List[str]:
    """
    Get all SLT files in the test directory.
    
    Returns:
        List of file paths
    """
    slt_files = []
    
    if not os.path.exists(slt_dir):
        print(f"Directory {slt_dir} does not exist")
        return slt_files
    
    for root, dirs, files in os.walk(slt_dir):
        for file in files:
            if file.endswith(".slt"):
                file_path = os.path.join(root, file)
                slt_files.append(file_path)
    
    return sorted(slt_files)


def main():
    """
    Main function to find all .slt files with positional parameters.
    """
    slt_dir = "test/sql"
    
    print(f"Searching for .slt files with positional parameters in {slt_dir}...")
    print("=" * 80)
    
    # Get all SLT files
    slt_files = get_all_slt_files(slt_dir)
    
    if not slt_files:
        print(f"No .slt files found in {slt_dir}")
        return
    
    print(f"Found {len(slt_files)} .slt files total")
    print()
    
    # Track files with positional parameters
    files_with_params = []
    total_matches = 0
    
    # Search each file for positional parameters
    for file_path in slt_files:
        matches = find_positional_params_in_file(file_path)
        
        if matches:
            files_with_params.append((file_path, matches))
            total_matches += len(matches)
    
    # Display results
    if files_with_params:
        print(f"Found {len(files_with_params)} files with positional parameters:")
        print(f"Total matches: {total_matches}")
        print()
        
        for file_path, matches in files_with_params:
            print(f"📁 {file_path}")
            
            # Group matches by unique parameters found
            all_params = set()
            for _, _, params in matches:
                all_params.update(params)
            
            print(f"   Parameters found: {sorted(all_params)}")
            print(f"   Occurrences: {len(matches)}")
            
            # Show first few matches as examples
            for i, (line_num, line_content, params) in enumerate(matches[:3]):
                print(f"   Line {line_num}: {line_content}")
                if len(line_content) > 80:
                    print(f"   Line {line_num}: {line_content[:77]}...")
                print(f"              → {', '.join(params)}")
            
            if len(matches) > 3:
                print(f"   ... and {len(matches) - 3} more occurrences")
            
            print()
    else:
        print("No .slt files found with positional parameters ($1, $2, etc.)")
    
    # Summary
    print("=" * 80)
    print("SUMMARY:")
    print(f"Total .slt files scanned: {len(slt_files)}")
    print(f"Files with positional parameters: {len(files_with_params)}")
    print(f"Total parameter occurrences: {total_matches}")
    
    if files_with_params:
        print("\nFiles with positional parameters:")
        for file_path, _ in files_with_params:
            print(f"  - {file_path}")


if __name__ == "__main__":
    main()
